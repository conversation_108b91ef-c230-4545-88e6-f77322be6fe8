// Jest Test Setup for Permanent Pro Status Tests

// Mock performance API for performance tests
global.performance = {
    now: jest.fn(() => Date.now())
};

// Mock console methods to reduce noise in tests
global.console = {
    ...console,
    log: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
};

// Mock Chrome Extension APIs
global.chrome = {
    storage: {
        sync: {
            get: jest.fn(),
            set: jest.fn(),
            remove: jest.fn()
        },
        local: {
            get: jest.fn(),
            set: jest.fn(),
            remove: jest.fn()
        }
    },
    runtime: {
        onInstalled: {
            addListener: jest.fn()
        },
        onStartup: {
            addListener: jest.fn()
        },
        sendMessage: jest.fn()
    }
};

// Mock Date for consistent testing
const mockDate = new Date('2025-01-15T10:30:00.000Z');
global.Date = class extends Date {
    constructor(...args) {
        if (args.length === 0) {
            return mockDate;
        }
        return new Date(...args);
    }
    
    static now() {
        return mockDate.getTime();
    }
};

// Test utilities
global.testUtils = {
    // Create a mock Pro status object
    createMockProStatus: (overrides = {}) => ({
        isPro: true,
        verified: true,
        permanent: true,
        verifiedAt: '2025-01-15T10:30:00.000Z',
        storedAt: '2025-01-15T10:30:00.000Z',
        keyHash: 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
        membershipDetails: {
            tier: 'pro',
            status: 'active',
            expiresAt: '2026-01-15T10:30:00.000Z',
            createdAt: '2025-01-15T10:30:00.000Z',
            usageCount: 5
        },
        ...overrides
    }),
    
    // Create a mock expired Pro status
    createExpiredProStatus: () => ({
        isPro: true,
        verified: true,
        permanent: true,
        verifiedAt: '2025-01-15T10:30:00.000Z',
        storedAt: '2025-01-15T10:30:00.000Z',
        keyHash: 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
        membershipDetails: {
            tier: 'pro',
            status: 'active',
            expiresAt: '2024-01-15T10:30:00.000Z', // Expired
            createdAt: '2024-01-15T10:30:00.000Z',
            usageCount: 5
        }
    }),
    
    // Wait for async operations
    waitFor: (ms = 0) => new Promise(resolve => setTimeout(resolve, ms)),
    
    // Reset all Chrome storage mocks
    resetChromeStorage: () => {
        chrome.storage.sync.get.mockClear();
        chrome.storage.sync.set.mockClear();
        chrome.storage.sync.remove.mockClear();
        chrome.storage.local.get.mockClear();
        chrome.storage.local.set.mockClear();
        chrome.storage.local.remove.mockClear();
    }
};

// Setup before each test
beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Reset Chrome storage
    testUtils.resetChromeStorage();
    
    // Reset console mocks
    console.log.mockClear();
    console.warn.mockClear();
    console.error.mockClear();
});

// Cleanup after each test
afterEach(() => {
    // Restore any mocked timers
    jest.useRealTimers();
});
