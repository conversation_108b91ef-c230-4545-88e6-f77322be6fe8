// Permanent Pro Status Storage System
// Eliminates repeated online validation checks for Pro users by implementing 
// permanent Pro status storage after first successful verification

import { hashKey } from '../security/hashUtils.js';

/**
 * Store permanent Pro status after first successful validation
 * @param {string} keyHash - Hashed key for identification
 * @param {Object} membershipDetails - Membership details from validation
 * @returns {Promise<boolean>} - Success status
 */
export async function storePermanentProStatus(keyHash, membershipDetails) {
    try {
        const permanentData = {
            isPro: true,
            verified: true,
            verifiedAt: new Date().toISOString(),
            keyHash: keyHash.substring(0, 16), // Store first 16 chars for identification
            membershipDetails: {
                tier: membershipDetails?.tier || 'pro',
                status: membershipDetails?.status || 'active',
                expiresAt: membershipDetails?.expiresAt || null,
                createdAt: membershipDetails?.createdAt || null,
                lastUsed: new Date().toISOString(),
                usageCount: membershipDetails?.usageCount || 0,
                daysRemaining: membershipDetails?.daysRemaining || null,
                isExpired: membershipDetails?.isExpired || false,
                notes: membershipDetails?.notes || null
            },
            permanent: true,
            storedAt: new Date().toISOString()
        };

        // Store in chrome.storage.sync for cross-device sync
        const storageKey = `permanentProStatus_${keyHash.substring(0, 16)}`;
        await chrome.storage.sync.set({
            [storageKey]: permanentData
        });

        console.log('✅ Permanent Pro status stored successfully');
        return true;
    } catch (error) {
        console.error('❌ Failed to store permanent Pro status:', error);
        return false;
    }
}

/**
 * Check if user has permanent Pro status
 * @param {string} keyHash - Hashed key for identification
 * @returns {Promise<Object>} - Permanent status result
 */
export async function checkPermanentProStatus(keyHash) {
    try {
        const storageKey = `permanentProStatus_${keyHash.substring(0, 16)}`;
        const result = await chrome.storage.sync.get([storageKey]);
        const permanentData = result[storageKey];

        if (!permanentData || !permanentData.permanent) {
            return {
                verified: false,
                isPro: false,
                message: 'No permanent Pro status found'
            };
        }

        // Check if membership has expired (if expiration date exists)
        if (permanentData.membershipDetails?.expiresAt) {
            const expirationDate = new Date(permanentData.membershipDetails.expiresAt);
            const now = new Date();
            
            if (now > expirationDate) {
                return {
                    verified: false,
                    isPro: false,
                    message: 'Permanent Pro status expired',
                    expired: true,
                    expiresAt: permanentData.membershipDetails.expiresAt
                };
            }
        }

        return {
            verified: true,
            isPro: true,
            message: 'Pro status verified (permanent)',
            membershipDetails: permanentData.membershipDetails,
            verifiedAt: permanentData.verifiedAt,
            storedAt: permanentData.storedAt,
            permanent: true
        };
    } catch (error) {
        console.error('❌ Failed to check permanent Pro status:', error);
        return {
            verified: false,
            isPro: false,
            message: 'Error checking permanent status'
        };
    }
}

/**
 * Remove permanent Pro status (for key removal)
 * @param {string} keyHash - Hashed key for identification
 * @returns {Promise<boolean>} - Success status
 */
export async function removePermanentProStatus(keyHash) {
    try {
        const storageKey = `permanentProStatus_${keyHash.substring(0, 16)}`;
        await chrome.storage.sync.remove([storageKey]);
        
        console.log('✅ Permanent Pro status removed successfully');
        return true;
    } catch (error) {
        console.error('❌ Failed to remove permanent Pro status:', error);
        return false;
    }
}

/**
 * Migrate existing cache to permanent storage
 * @returns {Promise<Object>} - Migration result
 */
export async function migrateCacheToPermanent() {
    try {
        console.log('🔄 Starting migration from cache to permanent storage...');
        
        // Check if user has existing Pro key but no permanent status
        const syncData = await chrome.storage.sync.get(['hustleProKey', 'hustleProStatus']);
        const localData = await chrome.storage.local.get();
        
        if (!syncData.hustleProKey) {
            return {
                success: true,
                migrated: false,
                message: 'No Pro key found, migration not needed'
            };
        }

        if (!syncData.hustleProStatus?.isPro) {
            return {
                success: true,
                migrated: false,
                message: 'User is not Pro, migration not needed'
            };
        }

        const keyHash = await hashKey(syncData.hustleProKey);
        const permanentStatus = await checkPermanentProStatus(keyHash);
        
        if (permanentStatus.verified) {
            return {
                success: true,
                migrated: false,
                message: 'Permanent status already exists'
            };
        }

        // Look for cached data to migrate
        const cacheKey = `proStatus_${keyHash.substring(0, 16)}`;
        const cachedData = localData[cacheKey];
        
        if (cachedData?.isPro) {
            // Migrate from cache to permanent storage
            const migrationSuccess = await storePermanentProStatus(keyHash, cachedData.membershipDetails);
            
            if (migrationSuccess) {
                console.log('✅ Successfully migrated existing Pro user to permanent storage');
                return {
                    success: true,
                    migrated: true,
                    message: 'Successfully migrated to permanent storage',
                    migratedFrom: 'cache'
                };
            }
        } else if (syncData.hustleProStatus?.isPro) {
            // Migrate from sync status to permanent storage
            const migrationSuccess = await storePermanentProStatus(keyHash, syncData.hustleProStatus.membershipDetails);
            
            if (migrationSuccess) {
                console.log('✅ Successfully migrated existing Pro user from sync status to permanent storage');
                return {
                    success: true,
                    migrated: true,
                    message: 'Successfully migrated to permanent storage',
                    migratedFrom: 'sync'
                };
            }
        }

        return {
            success: false,
            migrated: false,
            message: 'No valid Pro data found to migrate'
        };
        
    } catch (error) {
        console.error('❌ Migration failed:', error);
        return {
            success: false,
            migrated: false,
            message: `Migration failed: ${error.message}`
        };
    }
}

/**
 * Get all permanent Pro status entries (for debugging/admin)
 * @returns {Promise<Array>} - Array of permanent status entries
 */
export async function getAllPermanentProStatuses() {
    try {
        const allSyncData = await chrome.storage.sync.get();
        const permanentStatuses = [];
        
        for (const [key, value] of Object.entries(allSyncData)) {
            if (key.startsWith('permanentProStatus_') && value.permanent) {
                permanentStatuses.push({
                    storageKey: key,
                    keyHash: value.keyHash,
                    verifiedAt: value.verifiedAt,
                    storedAt: value.storedAt,
                    membershipDetails: value.membershipDetails
                });
            }
        }
        
        return permanentStatuses;
    } catch (error) {
        console.error('❌ Failed to get permanent Pro statuses:', error);
        return [];
    }
}

/**
 * Clear all permanent Pro status data (for debugging/reset)
 * @returns {Promise<boolean>} - Success status
 */
export async function clearAllPermanentProStatuses() {
    try {
        const allSyncData = await chrome.storage.sync.get();
        const keysToRemove = [];
        
        for (const key of Object.keys(allSyncData)) {
            if (key.startsWith('permanentProStatus_')) {
                keysToRemove.push(key);
            }
        }
        
        if (keysToRemove.length > 0) {
            await chrome.storage.sync.remove(keysToRemove);
            console.log(`✅ Cleared ${keysToRemove.length} permanent Pro status entries`);
        }
        
        return true;
    } catch (error) {
        console.error('❌ Failed to clear permanent Pro statuses:', error);
        return false;
    }
}
