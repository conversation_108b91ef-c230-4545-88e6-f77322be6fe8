// Pro key validation system
import { PRO_KEYS_ENDPOINT, PRO_KEYS_ENHANCED_ENDPOINT, PRO_VALIDATION_ENDPOINT, MEMBERSHIP_CONFIG } from '../../config.js';
import { hashKey } from '../security/hashUtils.js';
import { checkPermanentProStatus, storePermanentProStatus, migrateCacheToPermanent } from './permanentProStatus.js';

// Debouncing mechanism to prevent multiple simultaneous API calls
let validationInProgress = new Map(); // Track ongoing validations by key hash
let lastValidationTime = new Map(); // Track last validation time by key hash
const VALIDATION_COOLDOWN = 5000; // 5 seconds cooldown between validations

// PHASE 1 OPTIMIZATION: Extended cache settings for better UX
const CACHE_MAX_AGE = 24 * 60 * 60 * 1000; // 24 hours for better UX (was 2 hours)
const CACHE_REFRESH_THRESHOLD = 4 * 60 * 60 * 1000; // 4 hours (was 30 minutes) 
const IDLE_CACHE_EXTENSION = 7 * 24 * 60 * 60 * 1000; // 7 days for verified pro users
const BACKGROUND_REFRESH_INTERVAL = 15 * 60 * 60 * 1000; // Background refresh every 15 minutes

// Smart cache warming system
let cacheWarmingTimer = null;
let lastBackgroundRefresh = 0;

/**
 * Initialize smart cache warming system
 */
export async function initializeCacheWarming() {
    try {
        // Get current pro key to set up warming
        const result = await chrome.storage.sync.get(['hustleProKey']);
        const proKey = result.hustleProKey;
        
        if (proKey && proKey.trim().length > 0) {
            console.log('🔥 Initializing smart cache warming for pro validation');
            startBackgroundCacheWarming(proKey);
        }
    } catch (error) {
        console.warn('Failed to initialize cache warming:', error);
    }
}

/**
 * Start background cache warming for a pro key
 * @param {string} proKey - The pro key to warm cache for
 */
async function startBackgroundCacheWarming(proKey) {
    if (cacheWarmingTimer) {
        clearInterval(cacheWarmingTimer);
    }
    
    cacheWarmingTimer = setInterval(async () => {
        try {
            const now = Date.now();
            if (now - lastBackgroundRefresh < BACKGROUND_REFRESH_INTERVAL) {
                return; // Skip if refreshed recently
            }
            
            const keyHash = await hashKey(proKey);
            const cached = await getCachedProStatus(keyHash);
            
            // Check if cache needs warming
            if (!cached.lastValidated || 
                (now - new Date(cached.lastValidated).getTime()) > CACHE_REFRESH_THRESHOLD) {
                
                console.log('🔥 Background cache warming: refreshing pro status');
                await validateProKey(proKey, true); // Force validation for cache warming
                lastBackgroundRefresh = now;
            }
        } catch (error) {
            console.warn('Background cache warming failed:', error);
        }
    }, BACKGROUND_REFRESH_INTERVAL);
}

/**
 * Stop background cache warming
 */
export function stopCacheWarming() {
    if (cacheWarmingTimer) {
        clearInterval(cacheWarmingTimer);
        cacheWarmingTimer = null;
        console.log('🔥 Cache warming stopped');
    }
}

/**
 * PHASE 2: Initialize persistent cache warming using Chrome Alarms API
 * This survives browser restarts and popup closures
 */
export async function initializePersistentCacheWarming() {
    try {
        // Clear existing alarms
        await chrome.alarms.clear('proKeyValidation');
        
        // Get current pro key
        const result = await chrome.storage.sync.get(['hustleProKey']);
        const proKey = result.hustleProKey;
        
        if (proKey && proKey.trim().length > 0) {
            console.log('🔥 Setting up persistent cache warming');
            
            // Create persistent alarm for every 4 hours
            await chrome.alarms.create('proKeyValidation', {
                delayInMinutes: 60, // First validation in 1 hour
                periodInMinutes: 240 // Then every 4 hours
            });
            
            console.log('✅ Persistent cache warming enabled');
        }
    } catch (error) {
        console.warn('Failed to setup persistent cache warming:', error);
    }
}

/**
 * Validate a user's pro key against remote hashed keys (enhanced format first, fallback to simple)
 * @param {string} userKey - The user's plain text key
 * @param {boolean} forceValidation - Force validation even if recently validated
 * @returns {Promise<Object>} - Validation result with isPro status and membership details
 */
export async function validateProKey(userKey, forceValidation = false) {
    if (!userKey || userKey.trim().length === 0) {
        return {
            isPro: false,
            cached: false,
            message: 'No key provided'
        };
    }

    try {
        // Create a unique identifier for this key
        const keyHash = await hashKey(userKey);

        // NEW: Check permanent status first (unless forced validation)
        if (!forceValidation) {
            const permanentStatus = await checkPermanentProStatus(keyHash);
            if (permanentStatus.verified) {
                console.log('✅ Using permanent Pro status (no API call needed)');
                return {
                    isPro: true,
                    permanent: true,
                    cached: false,
                    message: 'Pro status verified (permanent)',
                    membershipDetails: permanentStatus.membershipDetails,
                    lastValidated: permanentStatus.verifiedAt,
                    verifiedAt: permanentStatus.verifiedAt,
                    storedAt: permanentStatus.storedAt
                };
            }

            // NEW: Try migration if no permanent status found but user might be Pro
            try {
                const migrationResult = await migrateCacheToPermanent();
                if (migrationResult.migrated) {
                    console.log('✅ Migration successful, checking permanent status again');
                    const newPermanentStatus = await checkPermanentProStatus(keyHash);
                    if (newPermanentStatus.verified) {
                        return {
                            isPro: true,
                            permanent: true,
                            cached: false,
                            migrated: true,
                            message: 'Pro status verified (permanent - migrated)',
                            membershipDetails: newPermanentStatus.membershipDetails,
                            lastValidated: newPermanentStatus.verifiedAt,
                            verifiedAt: newPermanentStatus.verifiedAt,
                            storedAt: newPermanentStatus.storedAt
                        };
                    }
                }
            } catch (migrationError) {
                console.warn('⚠️ Migration attempt failed, continuing with normal validation:', migrationError);
            }
        }

        // Check if validation is already in progress for this key
        if (validationInProgress.has(keyHash)) {
            console.log('🔄 Validation already in progress for this key, waiting...');
            return await validationInProgress.get(keyHash);
        }
        
        // PHASE 1: Smart cache checking with extended expiration for pro users
        if (!forceValidation) {
            const cachedResult = await getCachedProStatus(keyHash);
            const now = Date.now();
            
            // Check cooldown period
            const lastValidation = lastValidationTime.get(keyHash);
            if (lastValidation && (now - lastValidation) < VALIDATION_COOLDOWN) {
                console.log('🕒 Validation cooldown active, using cached result');
                return {
                    isPro: cachedResult.isPro || false,
                    cached: true,
                    message: cachedResult.isPro ? 'Pro user (cached - cooldown)' : 'Regular user (cached - cooldown)',
                    lastValidated: cachedResult.lastValidated || null,
                    membershipDetails: cachedResult.membershipDetails || null,
                    cooldown: true
                };
            }
            
            // ENHANCED: Different expiration for pro vs non-pro users
            if (cachedResult.lastValidated) {
                const cacheAge = now - new Date(cachedResult.lastValidated).getTime();
                let maxAge = CACHE_MAX_AGE;
                let refreshThreshold = CACHE_REFRESH_THRESHOLD;
                
                // Pro users get extended cache for better UX
                if (cachedResult.isPro && cachedResult.membershipDetails) {
                    maxAge = IDLE_CACHE_EXTENSION;
                    console.log('🚀 Using extended cached result for pro user (age:', Math.round(cacheAge / 60000), 'minutes)');
                }
                
                // Use cache if fresh enough
                if (cacheAge < refreshThreshold) {
                    console.log('🚀 Using fresh cached result (age:', Math.round(cacheAge / 60000), 'minutes)');
                    return {
                        isPro: cachedResult.isPro || false,
                        cached: true,
                        message: cachedResult.isPro ? 'Pro user (cached - fresh)' : 'Regular user (cached - fresh)',
                        lastValidated: cachedResult.lastValidated,
                        membershipDetails: cachedResult.membershipDetails || null,
                        fresh: true
                    };
                }
                
                // Use extended cache for pro users even if older
                if (cachedResult.isPro && cacheAge < maxAge) {
                    console.log('🚀 Using extended cached result for pro user (age:', Math.round(cacheAge / 60000), 'minutes)');
                    return {
                        isPro: cachedResult.isPro,
                        cached: true,
                        message: 'Pro user (cached - extended)',
                        lastValidated: cachedResult.lastValidated,
                        membershipDetails: cachedResult.membershipDetails || null,
                        extended: true
                    };
                }
            }
        }
        
        // Start validation and store the promise to prevent duplicates
        const validationPromise = performValidation(userKey, keyHash);
        validationInProgress.set(keyHash, validationPromise);
        
        try {
            const result = await validationPromise;

            // Update last validation time
            lastValidationTime.set(keyHash, Date.now());

            // NEW: Store permanent status after successful validation
            if (result.isPro && !result.cached) {
                try {
                    await storePermanentProStatus(keyHash, result.membershipDetails);
                    console.log('✅ Stored permanent Pro status for future use');
                    result.permanent = true;
                } catch (permanentError) {
                    console.warn('⚠️ Failed to store permanent status, continuing with normal flow:', permanentError);
                }
            }

            // Start cache warming if this is a pro user
            if (result.isPro && !cacheWarmingTimer) {
                startBackgroundCacheWarming(userKey);
            }

            return result;
        } finally {
            // Clean up the in-progress tracking
            validationInProgress.delete(keyHash);
        }

    } catch (error) {
        console.warn('All validation methods failed, checking cache:', error.message);
        
        // Fallback to cached result
        try {
            const userKeyHash = await hashKey(userKey);
            const cachedResult = await getCachedProStatus(userKeyHash);
            
            return {
                isPro: cachedResult.isPro || false,
                cached: true,
                message: cachedResult.isPro ? 'Pro user (cached)' : 'Regular user (cached)',
                lastValidated: cachedResult.lastValidated || null,
                membershipDetails: cachedResult.membershipDetails || null
            };
        } catch (cacheError) {
            console.error('Cache fallback failed:', cacheError);
            return {
                isPro: false,
                cached: false,
                message: 'Validation failed, defaulting to regular user'
            };
        }
    }
}

/**
 * Perform the actual validation logic with optimized parallel attempts
 * @param {string} userKey - The user's plain text key
 * @param {string} keyHash - Pre-computed key hash
 * @returns {Promise<Object>} - Validation result
 */
async function performValidation(userKey, keyHash) {
    console.log('🔍 Starting optimized validation for key:', keyHash.substring(0, 8) + '...');
    
    // Try Vercel API first (most secure and up-to-date)
    try {
        const vercelResult = await validateWithVercelAPI(userKey);
        if (vercelResult.found) {
            console.log('✅ Vercel API validation successful');
            return vercelResult;
        }
        
        // PHASE 3: If API suggests skipping to cache due to health issues
        if (vercelResult.skipToCache) {
            console.log('⚠️ API health check failed, using cache immediately');
            const cachedResult = await getCachedProStatus(keyHash);
            if (cachedResult.lastValidated) {
                return {
                    isPro: cachedResult.isPro || false,
                    cached: true,
                    message: 'Using cached result due to API latency',
                    lastValidated: cachedResult.lastValidated,
                    membershipDetails: cachedResult.membershipDetails || null,
                    healthFallback: true
                };
            }
        }
    } catch (error) {
        console.warn('Vercel API validation failed, trying fallbacks:', error.message);
    }

    // If Vercel fails, try enhanced and simple formats in parallel for faster response
    try {
        console.log('🚀 Running parallel validation fallbacks...');
        const [enhancedResult, simpleResult] = await Promise.allSettled([
            validateEnhancedFormat(userKey),
            validateSimpleFormat(userKey)
        ]);
        
        // Check enhanced format result first
        if (enhancedResult.status === 'fulfilled' && enhancedResult.value.found) {
            console.log('✅ Enhanced format validation successful');
            return enhancedResult.value;
        }
        
        // Fallback to simple format result
        if (simpleResult.status === 'fulfilled') {
            console.log('✅ Simple format validation completed');
            return simpleResult.value;
        }
        
        // If both failed, return the simple result (which handles the false case)
        return { isPro: false, cached: false, message: 'All validation methods failed' };
        
    } catch (error) {
        console.error('All validation methods failed:', error);
        return { isPro: false, cached: false, message: 'Validation error: ' + error.message };
    }
}

/**
 * Validate against Vercel API (primary method) with PHASE 3 health check
 * @param {string} userKey - The user's plain text key
 * @returns {Promise<Object>} - Vercel API validation result
 */
async function validateWithVercelAPI(userKey) {
    try {
        // Skip if endpoint is not configured (still has placeholder)
        if (PRO_VALIDATION_ENDPOINT.includes('your-app-name')) {
            console.log('Vercel endpoint not configured, skipping API validation');
            return { found: false };
        }

        // PHASE 3: Quick health check first (2 second timeout)
        try {
            console.log('🔍 Quick API health check...');
            const healthResponse = await fetch(`${PRO_VALIDATION_ENDPOINT.replace('/api', '')}/health`, {
                method: 'GET',
                signal: AbortSignal.timeout(2000) // 2 second timeout
            });
            
            if (!healthResponse.ok) {
                console.log('⚠️ API health check failed, using cache');
                return { found: false, skipToCache: true };
            }
            
            const healthData = await healthResponse.json();
            if (healthData.dbLatency > 1000) {
                console.log('⚠️ API latency too high, using cache');
                return { found: false, skipToCache: true };
            }
        } catch (healthError) {
            console.log('⚠️ API health check timeout, using cache');
            return { found: false, skipToCache: true };
        }

        console.log('✅ API healthy, proceeding with validation');

        const response = await fetch(`${PRO_VALIDATION_ENDPOINT}/validate-key`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({ 
                key: userKey,
                action: 'validate'
            }),
            signal: AbortSignal.timeout(5000) // 5 second timeout
        });

        if (!response.ok) {
            throw new Error(`Vercel API HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        
        if (result.success) {
            // Cache the result for offline use
            const userKeyHash = await hashKey(userKey);
            await cacheEnhancedProStatus(userKeyHash, result.isPro, result.membershipDetails);
            
            return {
                found: true,
                isPro: result.isPro,
                cached: false,
                message: result.message,
                lastValidated: new Date().toISOString(),
                membershipDetails: result.membershipDetails,
                enhanced: true,
                legacy: result.legacy || false,
                expired: result.expired || false
            };
        }
        
        return { found: false };
        
    } catch (error) {
        console.warn('Vercel API validation failed:', error.message);
        return { found: false };
    }
}

/**
 * Validate against enhanced membership format
 * @param {string} userKey - The user's plain text key
 * @returns {Promise<Object>} - Enhanced validation result
 */
async function validateEnhancedFormat(userKey) {
    try {
        const response = await fetch(PRO_KEYS_ENHANCED_ENDPOINT, {
            method: 'GET',
            cache: 'no-cache',
            headers: {
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`Enhanced endpoint HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        
        // Hash the user's key
        const userKeyHash = await hashKey(userKey, data.salt || undefined);
        
        // Check if key exists in enhanced format
        if (data.keys && data.keys[userKeyHash]) {
            const keyData = data.keys[userKeyHash];
            const now = new Date();
            const expiresAt = new Date(keyData.expiresAt);
            
            // Check if membership is expired
            const isExpired = now > expiresAt;
            const isPro = keyData.status === MEMBERSHIP_CONFIG.STATUS.ACTIVE && !isExpired;
            
            // Calculate days remaining
            const daysRemaining = Math.ceil((expiresAt - now) / (1000 * 60 * 60 * 24));
            
            // Update usage tracking
            await updateKeyUsage(userKeyHash, keyData);
            
            const membershipDetails = {
                status: isExpired ? MEMBERSHIP_CONFIG.STATUS.EXPIRED : keyData.status,
                tier: keyData.tier || MEMBERSHIP_CONFIG.TIERS.PRO,
                createdAt: keyData.createdAt,
                expiresAt: keyData.expiresAt,
                lastUsed: new Date().toISOString(),
                usageCount: (keyData.usageCount || 0) + 1,
                daysRemaining: Math.max(0, daysRemaining),
                isExpired: isExpired,
                notes: keyData.notes
            };
            
            // Cache the enhanced result
            await cacheEnhancedProStatus(userKeyHash, isPro, membershipDetails);
            
            return {
                found: true,
                isPro,
                cached: false,
                message: isPro ? 'Pro user validated' : (isExpired ? 'Membership expired' : 'Membership suspended'),
                lastValidated: new Date().toISOString(),
                membershipDetails,
                enhanced: true
            };
        }
        
        return { found: false };
        
    } catch (error) {
        console.warn('Enhanced format validation failed:', error.message);
        return { found: false };
    }
}

/**
 * Validate against simple format (backward compatibility)
 * @param {string} userKey - The user's plain text key
 * @returns {Promise<Object>} - Simple validation result
 */
async function validateSimpleFormat(userKey) {
    const response = await fetch(PRO_KEYS_ENDPOINT, {
        method: 'GET',
        cache: 'no-cache',
        headers: {
            'Accept': 'application/json'
        }
    });

    if (!response.ok) {
        throw new Error(`Simple endpoint HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    // Hash the user's key with the same salt
    const userKeyHash = await hashKey(userKey, data.salt || undefined);
    
    // Check if hash exists in pro list
    const isPro = data.proKeyHashes && data.proKeyHashes.includes(userKeyHash);
    
    // Cache the result for offline use
    await cacheProStatus(userKeyHash, isPro);
    
    return {
        isPro,
        cached: false,
        message: isPro ? 'Pro user validated (legacy)' : 'Regular user (default)',
        lastValidated: new Date().toISOString(),
        enhanced: false,
        legacy: true
    };
}

/**
 * Update key usage tracking (for enhanced format)
 * @param {string} keyHash - Hashed key
 * @param {Object} keyData - Current key data
 */
async function updateKeyUsage(keyHash, keyData) {
    try {
        // Store usage update locally for potential sync
        const usageData = {
            keyHash: keyHash.substring(0, 16),
            lastUsed: new Date().toISOString(),
            usageCount: (keyData.usageCount || 0) + 1,
            timestamp: Date.now()
        };
        
        await chrome.storage.local.set({
            [`usage_${keyHash.substring(0, 16)}`]: usageData
        });
    } catch (error) {
        console.warn('Failed to update usage tracking:', error);
    }
}

/**
 * Cache enhanced pro status for offline use
 * @param {string} keyHash - Hashed key for identification
 * @param {boolean} isPro - Pro status to cache
 * @param {Object} membershipDetails - Enhanced membership details
 */
async function cacheEnhancedProStatus(keyHash, isPro, membershipDetails) {
    try {
        const cacheData = {
            isPro,
            lastValidated: new Date().toISOString(),
            keyHash: keyHash.substring(0, 16),
            membershipDetails,
            enhanced: true
        };
        
        await chrome.storage.local.set({
            [`proStatus_${keyHash.substring(0, 16)}`]: cacheData
        });
    } catch (error) {
        console.warn('Failed to cache enhanced pro status:', error);
    }
}

/**
 * Cache pro status for offline use (simple format)
 * @param {string} keyHash - Hashed key for identification
 * @param {boolean} isPro - Pro status to cache
 */
async function cacheProStatus(keyHash, isPro) {
    try {
        const cacheData = {
            isPro,
            lastValidated: new Date().toISOString(),
            keyHash: keyHash.substring(0, 16), // Store partial hash for identification
            enhanced: false,
            legacy: true
        };
        
        await chrome.storage.local.set({
            [`proStatus_${keyHash.substring(0, 16)}`]: cacheData
        });
    } catch (error) {
        console.warn('Failed to cache pro status:', error);
    }
}

/**
 * Get cached pro status with smart expiration logic
 * @param {string} keyHash - Hashed key for identification
 * @returns {Promise<Object>} - Cached pro status
 */
async function getCachedProStatus(keyHash) {
    try {
        const result = await chrome.storage.local.get([`proStatus_${keyHash.substring(0, 16)}`]);
        const cached = result[`proStatus_${keyHash.substring(0, 16)}`];
        
        if (cached) {
            const now = Date.now();
            const cacheAge = now - new Date(cached.lastValidated).getTime();
            
            // ENHANCED: Different expiration for pro vs non-pro users
            let maxAge = CACHE_MAX_AGE;
            if (cached.isPro && cached.membershipDetails) {
                // Pro users get extended cache (better UX)
                maxAge = IDLE_CACHE_EXTENSION;
            }
            
            if (cacheAge < maxAge) {
                return {
                    ...cached,
                    fresh: cacheAge < CACHE_REFRESH_THRESHOLD
                };
            } else {
                console.log('🗑️ Cache expired (age:', Math.round(cacheAge / 60000), 'minutes), needs refresh');
            }
        }
        
        return { isPro: false, lastValidated: null, fresh: false };
    } catch (error) {
        console.warn('Failed to get cached pro status:', error);
        return { isPro: false, lastValidated: null, fresh: false };
    }
}

/**
 * Clear all cached pro status data
 */
export async function clearProCache() {
    try {
        const result = await chrome.storage.local.get(null);
        const keysToRemove = Object.keys(result).filter(key => key.startsWith('proStatus_'));
        
        if (keysToRemove.length > 0) {
            await chrome.storage.local.remove(keysToRemove);
        }
    } catch (error) {
        console.warn('Failed to clear pro cache:', error);
    }
} 