// Performance Validation Tests for Permanent Pro Status System
// Tests validation times to ensure <100ms target for permanent status checks

import { 
    checkPermanentProStatus, 
    storePermanentProStatus,
    migrateCacheToPermanent 
} from '../js/auth/permanentProStatus.js';
import { validate<PERSON>ro<PERSON>ey } from '../js/auth/proValidator.js';

// Mock Chrome Storage with realistic delays
const createRealisticStorageMock = (delay = 5) => ({
    sync: {
        data: {},
        get: jest.fn((keys) => {
            return new Promise(resolve => {
                setTimeout(() => {
                    const result = {};
                    if (Array.isArray(keys)) {
                        keys.forEach(key => {
                            if (createRealisticStorageMock.sync.data[key]) {
                                result[key] = createRealisticStorageMock.sync.data[key];
                            }
                        });
                    }
                    resolve(result);
                }, delay);
            });
        }),
        set: jest.fn((data) => {
            return new Promise(resolve => {
                setTimeout(() => {
                    Object.assign(createRealisticStorageMock.sync.data, data);
                    resolve();
                }, delay);
            });
        })
    }
});

// Mock hashKey with realistic delay
jest.mock('../js/security/hashUtils.js', () => ({
    hashKey: jest.fn((key) => {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve('abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890');
            }, 2); // 2ms for hashing
        });
    })
}));

describe('Performance Validation Tests', () => {
    let mockStorage;

    beforeEach(() => {
        mockStorage = createRealisticStorageMock(5); // 5ms storage delay
        global.chrome = { storage: mockStorage };
        jest.clearAllMocks();
    });

    describe('Permanent Status Check Performance', () => {
        test('should check permanent status in under 100ms', async () => {
            const keyHash = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
            
            // Setup permanent status
            mockStorage.sync.data['permanentProStatus_abcdef1234567890'] = testUtils.createMockProStatus();

            const startTime = performance.now();
            const result = await checkPermanentProStatus(keyHash);
            const endTime = performance.now();

            const duration = endTime - startTime;
            console.log(`Permanent status check took: ${duration.toFixed(2)}ms`);

            expect(result.verified).toBe(true);
            expect(duration).toBeLessThan(100); // Target from PRP
        });

        test('should handle non-existent permanent status quickly', async () => {
            const keyHash = 'nonexistent1234567890abcdef1234567890abcdef1234567890abcdef1234567890';

            const startTime = performance.now();
            const result = await checkPermanentProStatus(keyHash);
            const endTime = performance.now();

            const duration = endTime - startTime;
            console.log(`Non-existent status check took: ${duration.toFixed(2)}ms`);

            expect(result.verified).toBe(false);
            expect(duration).toBeLessThan(100);
        });
    });

    describe('Complete Validation Flow Performance', () => {
        test('should validate with permanent status in under 100ms', async () => {
            // Setup permanent status
            mockStorage.sync.data['permanentProStatus_abcdef1234567890'] = testUtils.createMockProStatus();

            const startTime = performance.now();
            const result = await validateProKey('test-pro-key-123');
            const endTime = performance.now();

            const duration = endTime - startTime;
            console.log(`Complete validation with permanent status took: ${duration.toFixed(2)}ms`);

            expect(result.isPro).toBe(true);
            expect(result.permanent).toBe(true);
            expect(duration).toBeLessThan(100);
        });
    });

    describe('Storage Performance', () => {
        test('should store permanent status efficiently', async () => {
            const keyHash = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
            const membershipDetails = {
                tier: 'pro',
                status: 'active',
                expiresAt: '2026-01-15T10:30:00.000Z'
            };

            const startTime = performance.now();
            const result = await storePermanentProStatus(keyHash, membershipDetails);
            const endTime = performance.now();

            const duration = endTime - startTime;
            console.log(`Permanent status storage took: ${duration.toFixed(2)}ms`);

            expect(result).toBe(true);
            expect(duration).toBeLessThan(200); // Allow more time for storage operations
        });
    });

    describe('Migration Performance', () => {
        test('should migrate cache to permanent storage efficiently', async () => {
            // Setup existing Pro key and status
            mockStorage.sync.data = {
                hustleProKey: 'test-pro-key-123',
                hustleProStatus: {
                    isPro: true,
                    lastChecked: '2025-01-15T10:30:00.000Z'
                }
            };

            // Mock local storage for cache
            global.chrome.storage.local = {
                get: jest.fn(() => Promise.resolve({
                    'proStatus_abcdef1234567890': {
                        isPro: true,
                        lastValidated: '2025-01-15T10:30:00.000Z',
                        membershipDetails: {
                            tier: 'pro',
                            status: 'active'
                        }
                    }
                }))
            };

            const startTime = performance.now();
            const result = await migrateCacheToPermanent();
            const endTime = performance.now();

            const duration = endTime - startTime;
            console.log(`Migration took: ${duration.toFixed(2)}ms`);

            expect(result.success).toBe(true);
            expect(result.migrated).toBe(true);
            expect(duration).toBeLessThan(500); // Allow more time for migration
        });
    });

    describe('Stress Testing', () => {
        test('should handle multiple concurrent permanent status checks', async () => {
            const keyHash = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
            
            // Setup permanent status
            mockStorage.sync.data['permanentProStatus_abcdef1234567890'] = testUtils.createMockProStatus();

            const concurrentChecks = 10;
            const promises = [];

            const startTime = performance.now();
            
            for (let i = 0; i < concurrentChecks; i++) {
                promises.push(checkPermanentProStatus(keyHash));
            }

            const results = await Promise.all(promises);
            const endTime = performance.now();

            const duration = endTime - startTime;
            const avgDuration = duration / concurrentChecks;
            
            console.log(`${concurrentChecks} concurrent checks took: ${duration.toFixed(2)}ms total, ${avgDuration.toFixed(2)}ms average`);

            // All should succeed
            results.forEach(result => {
                expect(result.verified).toBe(true);
            });

            // Average should still be under 100ms
            expect(avgDuration).toBeLessThan(100);
        });

        test('should handle rapid sequential checks efficiently', async () => {
            const keyHash = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
            
            // Setup permanent status
            mockStorage.sync.data['permanentProStatus_abcdef1234567890'] = testUtils.createMockProStatus();

            const sequentialChecks = 20;
            const durations = [];

            for (let i = 0; i < sequentialChecks; i++) {
                const startTime = performance.now();
                const result = await checkPermanentProStatus(keyHash);
                const endTime = performance.now();
                
                durations.push(endTime - startTime);
                expect(result.verified).toBe(true);
            }

            const avgDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length;
            const maxDuration = Math.max(...durations);
            
            console.log(`${sequentialChecks} sequential checks: avg ${avgDuration.toFixed(2)}ms, max ${maxDuration.toFixed(2)}ms`);

            // Average should be under 100ms
            expect(avgDuration).toBeLessThan(100);
            
            // Even the slowest should be reasonable
            expect(maxDuration).toBeLessThan(200);
        });
    });

    describe('Memory Usage', () => {
        test('should not leak memory during repeated operations', async () => {
            const keyHash = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
            
            // Setup permanent status
            mockStorage.sync.data['permanentProStatus_abcdef1234567890'] = testUtils.createMockProStatus();

            // Measure initial memory (if available)
            const initialMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;

            // Perform many operations
            for (let i = 0; i < 100; i++) {
                await checkPermanentProStatus(keyHash);
            }

            // Measure final memory (if available)
            const finalMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
            
            if (performance.memory) {
                const memoryIncrease = finalMemory - initialMemory;
                console.log(`Memory increase after 100 operations: ${memoryIncrease} bytes`);
                
                // Should not increase memory significantly (allow 1MB for test overhead)
                expect(memoryIncrease).toBeLessThan(1024 * 1024);
            }
        });
    });

    describe('Performance Benchmarks', () => {
        test('should meet all performance targets', async () => {
            const results = {
                permanentStatusCheck: 0,
                nonExistentCheck: 0,
                completeValidation: 0,
                storage: 0
            };

            // Test permanent status check
            const keyHash = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
            mockStorage.sync.data['permanentProStatus_abcdef1234567890'] = testUtils.createMockProStatus();

            let start = performance.now();
            await checkPermanentProStatus(keyHash);
            results.permanentStatusCheck = performance.now() - start;

            // Test non-existent check
            start = performance.now();
            await checkPermanentProStatus('nonexistent123');
            results.nonExistentCheck = performance.now() - start;

            // Test complete validation
            start = performance.now();
            await validateProKey('test-pro-key-123');
            results.completeValidation = performance.now() - start;

            // Test storage
            start = performance.now();
            await storePermanentProStatus(keyHash, { tier: 'pro', status: 'active' });
            results.storage = performance.now() - start;

            console.log('Performance Benchmark Results:');
            console.log(`- Permanent status check: ${results.permanentStatusCheck.toFixed(2)}ms`);
            console.log(`- Non-existent check: ${results.nonExistentCheck.toFixed(2)}ms`);
            console.log(`- Complete validation: ${results.completeValidation.toFixed(2)}ms`);
            console.log(`- Storage operation: ${results.storage.toFixed(2)}ms`);

            // All critical operations should meet the <100ms target
            expect(results.permanentStatusCheck).toBeLessThan(100);
            expect(results.nonExistentCheck).toBeLessThan(100);
            expect(results.completeValidation).toBeLessThan(100);
            
            // Storage can be slightly slower but should still be reasonable
            expect(results.storage).toBeLessThan(200);
        });
    });
});
